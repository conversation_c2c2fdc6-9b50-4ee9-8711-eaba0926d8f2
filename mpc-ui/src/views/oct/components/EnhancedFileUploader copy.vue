<template>
  <div class="enhanced-file-uploader">
    <!-- 上传文件组件 -->
    <uploader
      class="uploader-app"
      ref="uploader"
      :options="options"
      :autoStart="false"
      @files-added="handleFilesAdded"
      @file-removed="handleFileRemoved"
      @file-progress="handleFileProgress"
      @file-success="handleFileSuccess"
      @file-error="handleFileError"
      :file-status-text="fileStatusTextObj"
    >
      <uploader-unsupport></uploader-unsupport>

      <!-- 隐藏的选择按钮 -->
      <uploader-btn class="select-file-btn" :attrs="attrs" ref="uploadBtn">选择文件</uploader-btn>
      <uploader-btn class="select-folder-btn" :attrs="attrs" :directory="true" ref="uploadDirBtn">选择文件夹</uploader-btn>

      <!-- 拖拽上传区域 -->
      <uploader-drop>
        <div class="drop-zone">
          <div class="drop-icon">
            <i class="el-icon-upload"></i>
          </div>
          <div class="drop-text">
            <p class="drop-title">拖拽文件或文件夹到此处上传</p>
            <p class="drop-subtitle">
              或者
              <span class="upload-btn" @click="handleClickUploadBtn">选择文件</span>
              /
              <span class="upload-btn" @click="handleClickUploadDirBtn">选择文件夹</span>
            </p>
            <p class="drop-tips">支持 .zip, .rar 格式文件</p>
          </div>
        </div>
      </uploader-drop>

      <!-- 文件列表 -->
      <uploader-list>
        <template v-slot:default="props">
          <div class="file-panel" v-if="props.fileList.length || Object.keys(fileTree).length">
            <!-- 操作栏 -->
            <div class="operation-bar">
              <div class="file-count">
                <span class="total-info">
                  共 {{ totalFileCount }} 个文件
                  <span v-if="totalFileCount > 0" class="file-size">
                    （{{ formatFileSize(totalSize) }}）
                  </span>
                </span>
                <span v-if="totalFileCount > 0" class="upload-stats">
                  <span v-if="waitingFileCount > 0" class="waiting">
                    等待 {{ waitingFileCount }}
                  </span>
                  <span v-if="uploadingFileCount > 0" class="uploading">
                    上传中 {{ uploadingFileCount }}
                  </span>
                  <span v-if="pausedFileCount > 0" class="paused">
                    已暂停 {{ pausedFileCount }}
                  </span>
                  <span v-if="completedFileCount > 0" class="completed">
                    已完成 {{ completedFileCount }}
                  </span>
                  <span v-if="existsFileCount > 0" class="exists">
                    已存在 {{ existsFileCount }}
                  </span>
                  <span v-if="errorFileCount > 0" class="failed">
                    失败 {{ errorFileCount }}
                  </span>
                </span>
              </div>
              <div class="operation-buttons">
                <!-- <button
                  class="operation-btn primary-btn"
                  @click="startAllUploads"
                  :disabled="isUploading"
                >
                  全部开始
                </button>
                <button
                  class="operation-btn"
                  @click="pauseAllUploads"
                  :disabled="!isUploading"
                >
                  全部暂停
                </button> -->
                <button
                  class="operation-btn"
                  @click="clearCompleted"
                >
                  清除已完成
                </button>
              </div>
            </div>

            <!-- 文件树结构 -->
            <div class="file-tree">
              <!-- 表头 -->
              <div class="file-tree-header">
                <div class="header-name">文件名</div>
                <div class="header-size">大小</div>
                <div class="header-progress">进度</div>
                <div class="header-status">状态</div>
                <div class="header-actions">操作</div>
              </div>

              <!-- 文件列表 -->
              <div class="file-tree-body">
                <file-tree-node
                  v-for="(node, key) in fileTree"
                  :key="`${key}-${fileTreeVersion}`"
                  :node="node"
                  :node-key="key"
                  :level="0"
                  @toggle-folder="toggleFolder"
                  @remove-file="onFileRemove"
                  @pause-file="onPause"
                  @resume-file="onResume"
                  @retry-file="onRetry"
                />
              </div>
            </div>
          </div>
        </template>
      </uploader-list>
    </uploader>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5'
import { getToken } from "@/utils/auth";
import { mergeChunks } from "@/api/file/file";
import { createInfo, updateFileInfoStatus } from "@/api/oct/index";
import FileTreeNode from './FileTreeNode.vue';

// 常量定义
const CHUNK_SIZE = 20 * 1024 * 1024 // 每个分片的大小：20MB
const ALLOWED_EXTENSIONS = ['.zip', '.rar'] // 允许的文件扩展名

export default {
  name: 'EnhancedFileUploader',
  components: {
    FileTreeNode
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 上传组件配置项
      options: {
        target: process.env.VUE_APP_BASE_API + "/file/chunk",
        chunkSize: CHUNK_SIZE,
        fileParameterName: 'file',
        maxChunkRetries: 1,
        testChunks: true,
        headers: this.getAuthHeaders(),
        query: this.formData,
        checkChunkUploadedByResponse: this.checkChunkUploaded,
        parseTimeRemaining: this.parseTimeRemaining,
        processParams: (params) => params
      },
      attrs: {
        accept: ALLOWED_EXTENSIONS.join(', ')
      },
      fileStatusTextObj: {
        success: "上传成功",
        error: "上传失败",
        uploading: "正在上传",
        paused: "已暂停",
        waiting: "等待中",
      },
      filesList: [],
      fileTree: {}, // 文件树结构
      fileTreeVersion: 0, // 用于强制更新文件树
      isUploading: false
    }
  },
  computed: {
    // Uploader 上传组件实例
    uploaderInstance() {
      return this.$refs.uploader?.uploader
    },
    // 总文件数量
    totalFileCount() {
      return this.filesList.length
    },
    // 总文件大小
    totalSize() {
      return this.filesList.reduce((total, file) => total + (file.size || 0), 0)
    },

    // 获取所有文件节点（递归遍历文件树）
    allFileNodes() {
      // 依赖 fileTreeVersion 确保响应式更新
      this.fileTreeVersion

      const nodes = []
      const traverse = (tree) => {
        Object.values(tree).forEach(node => {
          if (node.type === 'file') {
            nodes.push(node)
          } else if (node.type === 'folder' && node.children) {
            traverse(node.children)
          }
        })
      }
      traverse(this.fileTree)
      return nodes
    },

    // 已完成文件数量
    completedFileCount() {
      return this.allFileNodes.filter(node =>
        node.status === 'success'
      ).length
    },

    // 文件已存在数量
    existsFileCount() {
      return this.allFileNodes.filter(node =>
        node.status === 'error' && node.statusText === '文件已存在'
      ).length
    },

    // 上传失败文件数量（不包括文件已存在）
    errorFileCount() {
      return this.allFileNodes.filter(node =>
        node.status === 'error' && node.statusText !== '文件已存在'
      ).length
    },

    // 失败文件数量（包括文件已存在，用于兼容）
    failedFileCount() {
      return this.existsFileCount + this.errorFileCount
    },

    // 上传中文件数量
    uploadingFileCount() {
      return this.allFileNodes.filter(node =>
        node.status === 'uploading'
      ).length
    },

    // 等待上传文件数量
    waitingFileCount() {
      return this.allFileNodes.filter(node =>
        node.status === 'waiting' || (!node.status && !node.md5Computing)
      ).length
    },

    // 暂停文件数量
    pausedFileCount() {
      return this.allFileNodes.filter(node =>
        node.status === 'paused'
      ).length
    }
  },
  methods: {
    // 获取认证头
    getAuthHeaders() {
      return { Authorization: "Bearer " + getToken() }
    },

    // 检查分片是否已上传
    checkChunkUploaded(chunk, response) {
      let result = JSON.parse(response)
      if (result.code && result.code != 200) {
        chunk.uploader.pause()
      }
      return (result.chunkNumbers || []).indexOf(chunk.offset + 1) >= 0
    },

    // 解析剩余时间
    parseTimeRemaining(_timeRemaining, parsedTimeRemaining) {
      return parsedTimeRemaining
        .replace(/\syears?/, "年")
        .replace(/\days?/, "天")
        .replace(/\shours?/, "小时")
        .replace(/\sminutes?/, "分钟")
        .replace(/\sseconds?/, "秒");
    },

    // 点击上传文件按钮
    handleClickUploadBtn() {
      this.$refs.uploadBtn.$el.click()
    },

    // 点击上传文件夹按钮
    handleClickUploadDirBtn() {
      this.$refs.uploadDirBtn.$el.click()
    },

    // 文件添加处理
    handleFilesAdded(files) {
      this.filesList = [...this.filesList, ...files]
      this.$emit('fileList', this.filesList)
      
      // 构建文件树
      this.buildFileTree(files)
      
      // 为每个文件计算MD5
      files.forEach((file) => {
        this.computeMD5(file)
      })
    },

    // 文件移除处理
    handleFileRemoved(file) {
      this.filesList = this.filesList.filter((item) => item.id != file.id)
      this.$emit('fileList', this.filesList)
      this.removeFileFromTree(file)
    },

    // 文件进度处理
    handleFileProgress(rootFile, file, _chunk) {
      // 检查文件是否已被取消或处于错误状态，如果是则不更新进度
      const path = file.relativePath || file.name
      const pathParts = path.split('/')

      let targetNode
      if (pathParts.length === 1) {
        targetNode = this.fileTree[pathParts[0]]
      } else {
        let parentLevel = this.fileTree
        for (let i = 0; i < pathParts.length - 1; i++) {
          if (parentLevel[pathParts[i]]) {
            parentLevel = parentLevel[pathParts[i]].children
          }
        }
        targetNode = parentLevel[pathParts[pathParts.length - 1]]
      }

      // 如果文件已经是错误状态（如文件已存在），则不更新进度
      if (targetNode && targetNode.status === 'error') {
        return
      }

      // 获取速度和剩余时间（优先使用 rootFile 的数据）
      const averageSpeed = rootFile.averageSpeed || file.averageSpeed || 0;
      const timeRemaining = (rootFile.timeRemaining && rootFile.timeRemaining()) ||
                           (file.timeRemaining && file.timeRemaining()) || 0;

      // 格式化速度和时间
      const formattedSpeed = this.formatSpeed(averageSpeed);
      const formattedTime = this.formatTimeRemaining(timeRemaining);

      this.updateFileInTree(file, {
        progress: Math.floor(file.progress() * 100),
        speed: formattedSpeed,
        timeRemaining: formattedTime,
        status: 'uploading'
      });
    },

    // 构建文件树结构
    buildFileTree(files) {
      files.forEach(file => {
        const path = file.relativePath || file.name
        const pathParts = path.split('/')

        let currentLevel = this.fileTree
        let currentPath = ''

        // 构建文件夹结构
        for (let i = 0; i < pathParts.length - 1; i++) {
          const folderName = pathParts[i]
          currentPath += (currentPath ? '/' : '') + folderName

          if (!currentLevel[folderName]) {
            currentLevel[folderName] = {
              type: 'folder',
              name: folderName,
              path: currentPath,
              expanded: true,
              children: {},
              files: []
            }
          }
          currentLevel = currentLevel[folderName].children
        }

        // 添加文件
        const fileName = pathParts[pathParts.length - 1]

        if (pathParts.length === 1) {
          // 根目录文件
          this.fileTree[fileName] = {
            type: 'file',
            name: fileName,
            path: path,
            file: file,
            status: 'waiting',
            progress: 0,
            speed: '',
            timeRemaining: '',
            statusText: '等待上传',
            md5Computing: false
          }
        } else {
          // 文件夹内文件
          let parentLevel = this.fileTree
          for (let i = 0; i < pathParts.length - 1; i++) {
            parentLevel = parentLevel[pathParts[i]].children
          }

          parentLevel[fileName] = {
            type: 'file',
            name: fileName,
            path: path,
            file: file,
            status: 'waiting',
            progress: 0,
            speed: '',
            timeRemaining: '',
            statusText: '等待上传',
            md5Computing: false
          }
        }
      })

      this.fileTreeVersion++
    },

    // 从文件树中移除文件
    removeFileFromTree(file) {
      const path = file.relativePath || file.name
      const pathParts = path.split('/')

      if (pathParts.length === 1) {
        // 根目录文件
        delete this.fileTree[pathParts[0]]
      } else {
        // 文件夹内文件
        let parentLevel = this.fileTree
        for (let i = 0; i < pathParts.length - 1; i++) {
          if (parentLevel[pathParts[i]]) {
            parentLevel = parentLevel[pathParts[i]].children
          }
        }
        delete parentLevel[pathParts[pathParts.length - 1]]
      }

      this.fileTreeVersion++
    },

    // 更新文件树中的文件信息
    updateFileInTree(file, updates) {
      const path = file.relativePath || file.name
      const pathParts = path.split('/')

      let targetNode
      if (pathParts.length === 1) {
        // 根目录文件
        targetNode = this.fileTree[pathParts[0]]
      } else {
        // 文件夹内文件
        let parentLevel = this.fileTree
        for (let i = 0; i < pathParts.length - 1; i++) {
          if (parentLevel[pathParts[i]]) {
            parentLevel = parentLevel[pathParts[i]].children
          }
        }
        targetNode = parentLevel[pathParts[pathParts.length - 1]]
      }

      if (targetNode) {
        Object.assign(targetNode, updates)
        this.fileTreeVersion++
      }
      
      this.$parent.$parent.uploadingFileCount = this.uploadingFileCount + this.pausedFileCount
    },

    // 计算文件MD5
    async computeMD5(file) {
      return new Promise((resolve) => {
        this.updateFileInTree(file, {
          statusText: '分片处理中 0%',
          md5Computing: true
        });

        let fileReader = new FileReader()
        let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
        let currentChunk = 0
        let chunks = Math.ceil(file.size / CHUNK_SIZE)
        let spark = new SparkMD5.ArrayBuffer()

        // 在自动上传模式下，暂停文件以便计算MD5
        file.pause()
        loadNext()

        fileReader.onload = (e) => {
          spark.append(e.target.result)
          if (currentChunk < chunks) {
            currentChunk++
            loadNext()
            this.updateFileInTree(file, {
              statusText: `分片处理中 ${((currentChunk / chunks) * 100).toFixed(0)}%`
            });
          } else {
            let md5 = spark.end()
            file.uniqueIdentifier = md5

            let params = {
              identifier: md5,
              manufacturerInfoId: this.formData.manufacturerInfoId,
              deviceTypeId: this.formData.deviceTypeId,
              fileSize: file.size,
              fileName: file.name
            }

            createInfo(params).then(res => {
              if (res.data.status == 1) {
                // file.cancel()
                this.updateFileInTree(file, {
                  status: 'error',
                  statusText: '文件已存在',
                  md5Computing: false
                });
                resolve()
              } else {
                this.updateFileInTree(file, {
                  statusText: '等待上传',
                  md5Computing: false
                });
                file.resume()
                resolve()
              }
            }).catch(error => {
              console.error('检查文件状态失败:', error)
              this.$message.error('检查文件状态失败，请重试')
              // file.cancel()
              this.updateFileInTree(file, {
                status: 'error',
                statusText: '检查失败',
                md5Computing: false
              });
              resolve()
            })
          }
        }

        fileReader.onerror = () => {
          this.$message.error(`文件${file.name}读取出错，请检查该文件`)
          file.cancel()
          resolve()
        }

        function loadNext() {
          let start = currentChunk * CHUNK_SIZE
          let end = start + CHUNK_SIZE >= file.size ? file.size : start + CHUNK_SIZE
          fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
        }
      })
    },

    // 文件上传成功处理
    handleFileSuccess(_rootFile, file, response) {
      let result = response ? JSON.parse(response) : ''

      if (result.code == 200) return

      const formData = new FormData();
      formData.append("identifier", file.uniqueIdentifier);
      formData.append("filename", file.name);
      formData.append("relativePath", file.relativePath);
      formData.append("totalSize", file.size);

      mergeChunks(formData).then((res) => {
        this.updateFileInTree(file, {
          status: 'success',
          statusText: '上传成功',
          progress: 100
        });

        if (Notification.permission === 'granted') {
          new Notification('文件上传完成', { body: file.name })
        }

        let params = {
          identifier: file.uniqueIdentifier,
          status: 1,
          fileUrl: res.data.fileUrl
        }
        updateFileInfoStatus(params)

        // 检查是否所有文件都已完成上传
        this.checkAllFilesCompleted()
      })
    },

    // 文件上传错误处理
    handleFileError(_rootFile, file, response) {
      console.log('文件上传失败:', file.name, response)
      this.updateFileInTree(file, {
        status: 'error',
        statusText: '上传失败'
      });
    },

    // 全部开始上传
    startAllUploads() {
      if (this.uploaderInstance) {
        this.isUploading = true
        this.uploaderInstance.upload()
      }
    },

    // 全部暂停上传
    pauseAllUploads() {
      if (this.uploaderInstance) {
        this.isUploading = false
        this.uploaderInstance.pause()
      }
    },

    // 清除已完成的文件
    clearCompleted() {
      const completedFiles = this.filesList.filter(file =>
        file.isComplete() || file.error
      )

      completedFiles.forEach(file => {
        file.cancel()
      })
    },

    // 检查所有文件是否完成
    checkAllFilesCompleted() {
      const allCompleted = this.filesList.every(file =>
        file.isComplete() || file.error
      )

      if (allCompleted) {
        this.isUploading = false
      }
    },

    // 切换文件夹展开/收起
    toggleFolder(nodeKey) {
      const pathParts = nodeKey.split('/')
      let currentLevel = this.fileTree

      for (let i = 0; i < pathParts.length; i++) {
        if (currentLevel[pathParts[i]]) {
          if (i === pathParts.length - 1) {
            currentLevel[pathParts[i]].expanded = !currentLevel[pathParts[i]].expanded
          } else {
            currentLevel = currentLevel[pathParts[i]].children
          }
        }
      }

      this.fileTreeVersion++
    },

    // 删除文件
    onFileRemove(file) {
      if (file && file.cancel) {
        file.cancel()
      }
    },

    // 暂停文件
    onPause(file) {
      if (file && file.pause) {
        file.pause()
        this.updateFileInTree(file, {
          status: 'paused',
          statusText: '已暂停'
        });
      }
    },

    // 恢复文件
    onResume(file) {
      if (file && file.resume) {
        file.resume()
        this.updateFileInTree(file, {
          status: 'uploading',
          statusText: '正在上传'
        });
      }
    },

    // 重试文件
    onRetry(file) {
      if (file && file.retry) {
        file.retry()
        this.updateFileInTree(file, {
          status: 'uploading',
          statusText: '正在上传'
        });
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 格式化上传速度
    formatSpeed(bytesPerSecond) {
      if (!bytesPerSecond || isNaN(bytesPerSecond) || bytesPerSecond <= 0) return '';

      const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
      let speed = bytesPerSecond;
      let index = 0;

      while (speed >= 1024 && index < units.length - 1) {
        speed /= 1024;
        index++;
      }

      return `${speed.toFixed(1)} ${units[index]}`;
    },

    // 格式化剩余时间
    formatTimeRemaining(seconds) {
      if (!seconds || isNaN(seconds) || seconds <= 0 || !isFinite(seconds)) return '';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
      } else {
        return `${secs}秒`;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.enhanced-file-uploader {
  .drop-zone {
    text-align: center;
    padding: 60px 20px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background-color: #fafafa;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }

    .drop-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .drop-title {
      font-size: 16px;
      color: #333;
      margin: 0 0 8px 0;
    }

    .drop-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0 0 8px 0;

      .upload-btn {
        color: #1890ff;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .drop-tips {
      font-size: 12px;
      color: #999;
      margin: 0;
    }
  }

  .file-panel {
    margin-top: 20px;

    .operation-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 12px;

      .file-count {
        font-size: 14px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 12px;

        .total-info {
          font-weight: 500;

          .file-size {
            color: #666;
            font-weight: normal;
          }
        }

        .upload-stats {
          display: flex;
          gap: 8px;
          font-size: 13px;

          span {
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
          }

          .uploading {
            background-color: #e6f7ff;
            color: #1890ff;
          }

          .completed {
            background-color: #f6ffed;
            color: #52c41a;
          }

          .failed {
            background-color: #fff2f0;
            color: #ff4d4f;
          }

          .waiting {
            background-color: #fafafa;
            color: #666;
          }

          .paused {
            background-color: #fff7e6;
            color: #fa8c16;
          }

          .exists {
            background-color: #f0f0f0;
            color: #8c8c8c;
          }
        }
      }

      .operation-buttons {
        display: flex;
        gap: 8px;

        .operation-btn {
          padding: 6px 12px;
          font-size: 13px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s ease;
          background: #fff;
          color: #606266;

          &:hover:not(:disabled) {
            background-color: #ecf5ff;
            border-color: #b3d8ff;
            color: #409eff;
          }

          &:active:not(:disabled) {
            transform: translateY(1px);
          }

          &:disabled {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }

        .primary-btn {
          background-color: #409eff;
          border-color: #409eff;
          color: #fff;

          &:hover:not(:disabled) {
            background-color: #66b1ff;
            border-color: #66b1ff;
            color: #fff;
          }

          &:disabled {
            background-color: #a0cfff;
            border-color: #a0cfff;
            color: #fff;
          }
        }

        .clear-btn {
          color: #f56c6c;
          border-color: #f56c6c;

          &:hover:not(:disabled) {
            background-color: #fef0f0;
            border-color: #f56c6c;
            color: #f56c6c;
          }
        }
      }
    }

    .file-tree {
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      background-color: #fff;

      .file-tree-header {
        display: flex;
        align-items: center;
        padding: 12px 10px; // 与内容保持完全一致
        background-color: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        font-weight: 600;
        font-size: 13px;
        color: #333;

        .header-name {
          flex: 0 0 200px;
          margin-right: 12px;
          // text-align: center;
        }

        .header-size {
          flex: 0 0 80px;
          // text-align: center;
          margin-right: 12px;
        }

        .header-progress {
          flex: 0 0 120px;
          margin-right: 12px;
          // text-align: center;
        }

        .header-status {
          flex: 0 0 200px; // 增加宽度以显示完整状态信息
          margin-right: 12px;
          // text-align: center;
        }

        .header-actions {
          flex: 1; // 占据剩余空间
          // text-align: center;
        }
      }

      .file-tree-body {
        max-height: 400px;
        overflow-y: auto;
      }
    }
  }
}

/* 隐藏默认按钮 */
.select-file-btn,
.select-folder-btn {
  display: none;
}

/* 全局样式覆盖 */
::v-deep {
  .uploader-drop {
    border: none !important;
  }

  .uploader-drop.uploader-drop-active {
    border: 2px dashed #1890ff !important;
    background-color: #f0f8ff !important;
  }
}
</style>
